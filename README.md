# DeepLedger Frontend

A modern React TypeScript application for solo business owners to manage their accounting with AI assistance.

## 🚀 Key Features

- **AI-Powered Chat Interface**: Natural language transaction recording using Mastra agents
- **Authentication**: Supabase Auth with email verification and profile management
- **Responsive Design**: Mobile-first design with dark/light theme support
- **Real-time Communication**: Server-Sent Events (SSE) for live agent interactions

## 🛠 Tech Stack

- **React 19** with TypeScript
- **Vite** for fast development and builds
- **Tailwind CSS** for styling
- **Zustand** for state management
- **Supabase** for authentication
- **Mastra** for AI agent communication

## 📁 Project Structure

```
frontend/
├── src/
│   ├── components/         # React components by feature
│   │   ├── auth/          # Authentication (SignIn, SignUp, Landing)
│   │   ├── chat/          # Chat interface and messaging
│   │   ├── dashboard/     # Dashboard layout and overview
│   │   └── ui/            # Reusable UI components
│   ├── services/          # External integrations
│   │   ├── mastra-client.ts # AI backend communication
│   │   └── supabase.ts    # Authentication service
│   ├── stores/            # Zustand state management
│   │   ├── auth-store.ts  # User authentication state
│   │   └── chat-store.ts  # Chat and messaging state
│   ├── types/             # TypeScript definitions
│   └── utils/             # Helper functions
├── package.json           # Dependencies and scripts
└── vite.config.ts         # Build configuration
```

## 🚦 Getting Started

### Prerequisites
- Node.js 18+
- npm, yarn, or pnpm

### Quick Setup

1. **Install dependencies**:
   ```bash
   npm install
   ```

2. **Environment setup**:
   Create `.env` file:
   ```env
   VITE_SUPABASE_URL=https://iajycvybkkrwmhkompec.supabase.co
   VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
   ```

3. **Start development**:
   ```bash
   npm run dev
   ```
   Open `http://localhost:5173`

### Available Scripts
- `npm run dev` - Development server
- `npm run build` - Production build
- `npm run lint` - Code quality checks

## 🏗 Architecture

### Component Organization
- **Feature Components**: Business logic by domain (`auth/`, `chat/`, `dashboard/`)
- **UI Components**: Reusable generic components (`ui/`)
- **Services**: External API integrations (`mastra-client.ts`, `supabase.ts`)

### State Management (Zustand)
- **Auth Store**: User authentication and profile data
- **Chat Store**: AI agent communication and chat history
- **App Store**: Global UI state and navigation

### Key Integrations
- **Mastra Backend**: AI agent communication via HTTP/SSE
- **Supabase**: Authentication and user management

## 🎨 Design & Themes

### Theme Support
- Light, dark, and system themes
- CSS custom properties for theme-aware colors
- Automatic theme switching

### Responsive Design
- Mobile-first approach
- Tailwind CSS breakpoints (`md:`, `lg:`, `xl:`)
- Touch-friendly mobile interface

## 🔐 Authentication

Supabase Auth features:
- Email/password authentication
- Email verification and password reset
- Profile management
- Protected routes

📖 **See [AUTH_README.md](./AUTH_README.md) for detailed setup**

## 🤖 AI Integration

### Mastra Communication
```typescript
import mastraClient from './services/mastra-client';

// Send message with streaming
await mastraClient.sendMessageStream(
  message,
  { threadId },
  (chunk) => console.log('Chunk:', chunk),
  (toolCall) => console.log('Tool call:', toolCall)
);
```

### Real-time Features
- Server-Sent Events (SSE) for live responses
- Tool call visualization
- Connection status monitoring

📖 **See [SIMPLIFIED_CHAT.md](./SIMPLIFIED_CHAT.md) for chat system details**

## 🧪 Development Guidelines

### Code Style
- TypeScript strict mode
- Functional components with hooks
- Clear prop interfaces
- Proper error handling and loading states

### File Naming
- **Components**: PascalCase (`ChatInterface.tsx`)
- **Utilities**: camelCase (`formatCurrency.ts`)
- **Stores**: kebab-case (`auth-store.ts`)

### Component Best Practices
1. Single responsibility principle
2. TypeScript interfaces for props
3. Error boundaries and loading states
4. WCAG accessibility guidelines

## 🚀 Deployment

### Build Process
```bash
npm run build  # Creates dist/ directory
```

### Environment Variables
```env
VITE_SUPABASE_URL=your_production_supabase_url
VITE_SUPABASE_ANON_KEY=your_production_supabase_key
```

## 📚 Documentation

- **[AUTH_README.md](./AUTH_README.md)** - Authentication setup and usage
- **[SIMPLIFIED_CHAT.md](./SIMPLIFIED_CHAT.md)** - Chat system implementation

---

**DeepLedger Frontend** - Modern accounting application with AI assistance
