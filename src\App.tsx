import { useEffect } from 'react';
import { useAuthStore } from './stores/auth-store';
import { useChatStore } from './stores/chat-store';
import { LandingPage } from './components/auth/LandingPage';
import { DashboardLayout } from './components/dashboard/DashboardLayout';
import { ThemeProvider } from './contexts/ThemeContext';
import mastraClient from './services/mastra-client';

function App() {
  const { user, session, loading, initialized, initialize } = useAuthStore();
  const { setConnected, setError } = useChatStore();

  useEffect(() => {
    // Initialize authentication
    initialize();
  }, [initialize]);

  useEffect(() => {
    // Set up mock Mastra connection when user is authenticated
    if (user && initialized) {
      console.log('🔗 Setting up mock Mastra connection...');

      // Set authentication token for mock client
      if (session?.access_token) {
        mastraClient.setAuthToken(session.access_token);
        setConnected(true);
        setError(null);
        console.log('✅ Mock Mastra client authenticated');
      } else {
        // Even without token, allow mock functionality
        mastraClient.setAuthToken(null);
        setConnected(true);
        setError(null);
        console.log('✅ Mock Mastra client ready (no token)');
      }
    } else if (initialized && !user) {
      // User not authenticated - still allow mock functionality for testing
      setConnected(false);
      setError('Please sign in to use the chat features');
    }
  }, [user, session, initialized, setConnected, setError]);

  // Show loading spinner while initializing
  if (!initialized || loading) {
    return (
      <ThemeProvider>
        <div className="min-h-screen bg-background text-foreground flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-muted-foreground">Loading...</p>
          </div>
        </div>
      </ThemeProvider>
    );
  }

  // Show landing page if not authenticated
  if (!user) {
    return (
      <ThemeProvider>
        <LandingPage />
      </ThemeProvider>
    );
  }

  // Show dashboard if authenticated
  return (
    <ThemeProvider>
      <div className="min-h-screen bg-background text-foreground">
        <DashboardLayout />
      </div>
    </ThemeProvider>
  );
}

export default App;
