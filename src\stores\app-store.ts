import { create } from 'zustand';
import { persist } from 'zustand/middleware';

export type ViewType = 'dashboard' | 'chat' | 'transactions' | 'reports' | 'settings' | 'profile';

interface AppState {
  isLoading: boolean;
  error: string | null;
  currentView: ViewType;
}

interface AppActions {
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  clearError: () => void;
  setCurrentView: (view: ViewType) => void;
}

type AppStore = AppState & AppActions;

export const useAppStore = create<AppStore>()(
  persist(
    (set, get) => ({
      // Initial state
      isLoading: false,
      error: null,
      currentView: 'dashboard',

      // Actions
      setLoading: (loading) =>
        set({ isLoading: loading }),

      setError: (error) =>
        set({ error }),

      clearError: () =>
        set({ error: null }),

      setCurrentView: (view) =>
        set({ currentView: view }),
    }),
    {
      name: 'deepledger-app-store',
      partialize: (state) => ({
        currentView: state.currentView,
      }),
    }
  )
);
