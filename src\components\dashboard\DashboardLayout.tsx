
import React from 'react';
import { Header } from '../ui/Header';
import { AgentToolsSidebar } from '../ui/AgentToolsSidebar';
import { useAppStore } from '../../stores/app-store';
import { ChatInterface } from '../chat/ChatInterface';
import { ProfilePage } from './ProfilePage';
import { DashboardOverview } from './DashboardOverview';
import { TransactionsView } from '../transactions/TransactionsView';
import { ReportsView } from '../reports/ReportsView';
import { SettingsView } from './SettingsView';

export function DashboardLayout() {
  const { currentView } = useAppStore();

  const renderContent = () => {
    switch (currentView) {
      case 'dashboard':
        return <DashboardOverview />;
      case 'transactions':
        return <TransactionsView />;
      case 'reports':
        return <ReportsView />;
      case 'profile':
        return <ProfilePage />;
      case 'settings':
        return <SettingsView />;
      default:
        return <DashboardOverview />;
    }
  };

  return (
    <div className="flex flex-col h-screen bg-background text-foreground transition-colors duration-200">
      {/* Header Navigation */}
      <Header />

      {/* Main Layout with Sidebar */}
      <div className="flex flex-1 overflow-hidden relative">
        {/* Agent Tools Sidebar */}
        <AgentToolsSidebar />

        {/* Main Content Area */}
        <main className="flex-1 overflow-hidden relative">
          {currentView === 'chat' ? (
            <ChatInterface />
          ) : (
            <div className="h-full p-6 bg-background text-foreground overflow-auto animate-fade-in">
              <div className="max-w-7xl mx-auto">
                {renderContent()}
              </div>
            </div>
          )}
        </main>
      </div>
    </div>
  );
}