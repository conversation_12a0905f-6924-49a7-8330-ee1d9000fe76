# DeepLedger Authentication Setup

## Overview

DeepLedger now includes a complete authentication system using Supabase Auth with the following features:

- **Landing Page**: Beautiful marketing page with feature highlights
- **Sign Up**: User registration with email verification
- **Sign In**: User login with password reset functionality
- **Profile Page**: User profile management within the dashboard
- **Protected Routes**: Dashboard only accessible to authenticated users

## Features Implemented

### ✅ Authentication Flow
- Landing page with call-to-action
- Sign up form with password validation
- Sign in form with show/hide password
- Password reset functionality
- Email verification workflow
- Automatic session management

### ✅ Dashboard Integration
- Profile page in sidebar navigation
- User profile editing
- Account information display
- Sign out functionality
- Protected dashboard access

### ✅ Coming Soon Placeholders
- All other features (transactions, reports, settings) show "Coming Soon"
- Notification preferences
- Security settings
- Data export
- Account deletion

## File Structure

```
frontend/src/
├── components/
│   ├── auth/
│   │   ├── LandingPage.tsx      # Marketing landing page
│   │   ├── SignInForm.tsx       # Sign in form
│   │   └── SignUpForm.tsx       # Sign up form
│   └── dashboard/
│       └── ProfilePage.tsx      # User profile management
├── services/
│   └── supabase.ts             # Supabase client and auth helpers
├── stores/
│   └── auth-store.ts           # Authentication state management
└── App.tsx                     # Main app with auth routing
```

## Environment Configuration

The frontend uses these environment variables (already configured):

```env
VITE_SUPABASE_URL=https://iajycvybkkrwmhkompec.supabase.co
VITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

## Usage

### For Users
1. Visit the application
2. See the landing page with features
3. Click "Get Started" or "Sign Up" to create an account
4. Check email for verification link
5. Sign in to access the dashboard
6. Use the Profile page to manage account settings

### For Developers

#### Authentication State
```typescript
import { useAuthStore } from '../stores/auth-store';

const { user, session, loading, signIn, signUp, signOut } = useAuthStore();
```

#### Checking Authentication
```typescript
// In components
if (!user) {
  // User not authenticated
  return <LandingPage />;
}

// User is authenticated
return <Dashboard />;
```

#### Profile Management
```typescript
const { updateProfile } = useAuthStore();

await updateProfile({
  full_name: 'New Name',
  avatar_url: 'https://example.com/avatar.jpg'
});
```

## Backend Integration

The authentication system is designed to work seamlessly with the existing Mastra backend:

- **No Conflicts**: Frontend handles auth UI, backend handles business logic
- **Shared Database**: Both use the same Supabase project
- **JWT Validation**: Backend can validate frontend JWT tokens
- **Independent Development**: Teams can work simultaneously

## Security Features

- **Password Requirements**: Minimum 8 characters, uppercase, lowercase, number
- **Email Verification**: Required for account activation
- **Session Management**: Automatic token refresh
- **Secure Storage**: Tokens stored securely in browser
- **Password Reset**: Secure email-based password reset

## Next Steps

1. **Test the authentication flow** by creating an account
2. **Set up email templates** in Supabase for better branding
3. **Add profile picture upload** functionality
4. **Implement additional security features** (2FA, etc.)
5. **Connect backend API** to validate JWT tokens

## Troubleshooting

### Common Issues

1. **Email not received**: Check spam folder, verify Supabase email settings
2. **Sign up fails**: Check password requirements, verify email format
3. **Session not persisting**: Check browser storage, verify Supabase configuration

### Development

- Frontend runs on `http://localhost:5173`
- All authentication state is managed by Zustand store
- Supabase handles all auth backend functionality
- No additional backend auth setup required

## Team Coordination

- **Frontend Team**: Focus on UI/UX improvements, additional auth features
- **Backend Team**: Implement JWT validation, business logic APIs
- **Shared**: Use the same Supabase project for consistency
